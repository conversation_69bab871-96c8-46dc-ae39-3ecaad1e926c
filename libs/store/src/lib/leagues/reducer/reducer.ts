import { signalStoreFeature, type } from '@ngrx/signals';
import { on, withReducer } from '@ngrx/signals/events';
import { leaguesApiEvents, leaguesEvents } from '../events/events';
import { LeaguesState } from '../state/state';

export function withLeaguesReducer() {
    return signalStoreFeature(
        {
            state: type<LeaguesState>(),
        },
        withReducer(
            on(leaguesEvents.fetchLeagues, ({ payload }) => ({
                selectedCountryId: payload.countryId,
                selectedSeason: payload.season,
                loading: true,
                error: null,
            })),
            on(leaguesApiEvents.leaguesLoaded, ({ payload }) => ({
                loading: false,
                leagues: payload.leagues,
            })),
            on(leaguesApiEvents.leaguesLoadFailed, ({ payload }) => ({
                loading: false,
                error: payload.error,
            })),
        ),
    );
}
